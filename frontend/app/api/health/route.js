import { NextResponse } from 'next/server';

/**
 * Health check endpoint for deployment monitoring
 * Returns basic application health status and timestamp
 */
export async function GET() {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: Date.now(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      // Add deployment-specific info if available
      deploymentId: process.env.VERCEL_GIT_COMMIT_SHA || 
                   process.env.DEPLOYMENT_ID || 
                   'local',
      buildTime: process.env.BUILD_TIME || new Date().toISOString()
    };

    return NextResponse.json(healthData, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        timestamp: Date.now(),
        error: 'Health check failed'
      },
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
  }
}

// Support HEAD requests for basic health checks
export async function HEAD() {
  return new Response(null, { 
    status: 200,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  });
}
