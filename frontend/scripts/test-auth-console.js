/**
 * Console-based Authentication Testing Script
 * Copy và paste vào browser console để test authentication flow
 * 
 * Usage:
 * 1. Mở browser console (F12)
 * 2. Copy toàn bộ script này
 * 3. Paste vào console và nhấn Enter
 * 4. Chạy: runConsoleAuthTests()
 */

// Import functions (sẽ work nếu đã load page)
const { 
  clearAuthCookies, 
  setAuthCookies, 
  getAuthCookies, 
  isAuthenticated,
  debugAuthCookies 
} = window.cookieHelper || {};

const { 
  setupTestScenario,
  testAuthenticationState,
  runAuthenticationTests,
  TEST_SCENARIOS,
  MOCK_USERS
} = window.authTestHelper || {};

// Console testing functions
window.consoleAuthTests = {
  
  // Test 1: Cookie Management
  testCookieManagement() {
    console.group('🍪 Testing Cookie Management');
    
    try {
      // Clear cookies
      console.log('1. Clearing cookies...');
      clearAuthCookies();
      
      // Check if cleared
      const afterClear = getAuthCookies();
      console.log('After clear:', afterClear);
      
      // Set test data
      console.log('2. Setting test auth data...');
      const testData = {
        token: 'test_token_123',
        user: { id: 1, email: '<EMAIL>', fullname: 'Test User' },
        hasCompletedOrder: true,
        completedOrderInfo: { id: 1, course_name: 'Test Course' }
      };
      
      setAuthCookies(testData);
      
      // Verify data
      const afterSet = getAuthCookies();
      console.log('After set:', afterSet);
      
      // Test authentication check
      const authStatus = isAuthenticated();
      console.log('Authentication status:', authStatus);
      
      console.log('✅ Cookie management test completed');
      return true;
      
    } catch (error) {
      console.error('❌ Cookie management test failed:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  },

  // Test 2: Middleware Logic Simulation
  testMiddlewareLogic() {
    console.group('🛡️ Testing Middleware Logic');
    
    try {
      const testCases = [
        { path: '/', hasOrder: false, expected: 'allow' },
        { path: '/', hasOrder: true, expected: 'allow' },
        { path: '/quan-ly', hasOrder: false, expected: 'redirect_home' },
        { path: '/quan-ly', hasOrder: true, expected: 'allow' },
        { path: '/dang-nhap', hasOrder: false, expected: 'redirect_home' },
        { path: '/dang-nhap', hasOrder: true, expected: 'redirect_management' },
        { path: '/some-random-page', hasOrder: true, expected: 'redirect_management' }
      ];
      
      testCases.forEach((testCase, index) => {
        console.log(`Test ${index + 1}: ${testCase.path} (hasOrder: ${testCase.hasOrder})`);
        
        // Simulate middleware logic
        const { path, hasOrder } = testCase;
        let result = 'allow';
        
        // Simulate user authentication
        const isAuthenticated = true; // Assume authenticated for these tests
        
        if (isAuthenticated && hasOrder) {
          const allowedPages = ['/', '/khoa-hoc', '/bai-viet', '/hoc-tai-trung-tam', '/quan-ly', '/tai-khoan', '/thong-bao'];
          const isAllowedPage = allowedPages.some(allowed => 
            path === allowed || path.startsWith(allowed + '/')
          );
          
          if (!isAllowedPage) {
            result = 'redirect_management';
          }
        } else if (isAuthenticated && !hasOrder && path === '/quan-ly') {
          result = 'redirect_home';
        } else if (isAuthenticated && ['/dang-nhap', '/dang-ky'].includes(path)) {
          result = hasOrder ? 'redirect_management' : 'redirect_home';
        }
        
        const passed = result === testCase.expected;
        console.log(`  Expected: ${testCase.expected}, Got: ${result} ${passed ? '✅' : '❌'}`);
      });
      
      console.log('✅ Middleware logic test completed');
      return true;
      
    } catch (error) {
      console.error('❌ Middleware logic test failed:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  },

  // Test 3: Toast System
  testToastSystem() {
    console.group('🔔 Testing Toast System');

    try {
      // Check if toast context is available through React DevTools or global
      const hasToastContext = typeof window.React !== 'undefined' ||
                             document.querySelector('[data-testid="toast-container"]') !== null ||
                             typeof window.toast !== 'undefined';

      if (hasToastContext) {
        console.log('✅ Toast system context detected');
      } else {
        console.log('⚠️ Toast system context not detected (may be normal in console)');
      }

      // Check if ToastProvider is in the component tree
      const hasToastProvider = document.body.innerHTML.includes('ToastProvider') ||
                               document.querySelector('[class*="toast"]') !== null;

      console.log(`Toast Provider in DOM: ${hasToastProvider ? '✅ Yes' : '⚠️ No'}`);

      return true;

    } catch (error) {
      console.error('❌ Toast system test failed:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  },

  // Test 4: API Health Check
  async testHealthAPI() {
    console.group('🏥 Testing Health API');
    
    try {
      console.log('Calling /api/health...');
      const response = await fetch('/api/health');
      const data = await response.json();
      
      console.log('Health API Response:', data);
      
      const isHealthy = response.ok && data.status === 'healthy';
      console.log(`Health Status: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
      
      return isHealthy;
      
    } catch (error) {
      console.error('❌ Health API test failed:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  },

  // Test 5: Local Storage Cleanup
  testLocalStorageCleanup() {
    console.group('🧹 Testing Local Storage Cleanup');
    
    try {
      // Set some test data
      localStorage.setItem('user', JSON.stringify({ id: 1, name: 'Test' }));
      localStorage.setItem('user_data', JSON.stringify({ id: 1, name: 'Test' }));
      localStorage.setItem('auth_token', 'test_token');
      
      console.log('Before cleanup:', {
        user: localStorage.getItem('user'),
        user_data: localStorage.getItem('user_data'),
        auth_token: localStorage.getItem('auth_token')
      });
      
      // Clear auth cookies (should also clear localStorage)
      clearAuthCookies();
      
      console.log('After cleanup:', {
        user: localStorage.getItem('user'),
        user_data: localStorage.getItem('user_data'),
        auth_token: localStorage.getItem('auth_token')
      });
      
      const isClean = !localStorage.getItem('user') && 
                     !localStorage.getItem('user_data') && 
                     !localStorage.getItem('auth_token');
      
      console.log(`Cleanup Status: ${isClean ? '✅ Clean' : '❌ Not Clean'}`);
      return isClean;
      
    } catch (error) {
      console.error('❌ Local storage cleanup test failed:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  },

  // Run all tests
  async runAllTests() {
    console.group('🧪 Running All Console Authentication Tests');
    console.log('Starting comprehensive authentication tests...\n');
    
    const results = {
      cookieManagement: false,
      middlewareLogic: false,
      toastSystem: false,
      healthAPI: false,
      localStorageCleanup: false
    };
    
    // Run tests sequentially
    results.cookieManagement = this.testCookieManagement();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    results.middlewareLogic = this.testMiddlewareLogic();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    results.toastSystem = this.testToastSystem();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    results.healthAPI = await this.testHealthAPI();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    results.localStorageCleanup = this.testLocalStorageCleanup();
    
    // Summary
    console.log('\n📊 Test Results Summary:');
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`${test}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
    });
    
    const allPassed = Object.values(results).every(result => result);
    console.log(`\nOverall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    console.groupEnd();
    return results;
  }
};

// Quick access function
window.runConsoleAuthTests = () => window.consoleAuthTests.runAllTests();

// Auto-expose helper functions if available
if (typeof clearAuthCookies !== 'undefined') {
  window.cookieHelper = {
    clearAuthCookies,
    setAuthCookies,
    getAuthCookies,
    isAuthenticated,
    debugAuthCookies
  };
}

console.log(`
🧪 Console Authentication Tests Loaded!

Available commands:
- runConsoleAuthTests()                    // Run all tests
- consoleAuthTests.testCookieManagement()  // Test cookie functions
- consoleAuthTests.testMiddlewareLogic()   // Test middleware logic
- consoleAuthTests.testToastSystem()       // Test toast notifications
- consoleAuthTests.testHealthAPI()         // Test health API
- consoleAuthTests.testLocalStorageCleanup() // Test cleanup

Quick start: runConsoleAuthTests()
`);
