#!/usr/bin/env node

/**
 * Node.js Authentication Testing Script
 * Test authentication improvements without browser UI
 * 
 * Usage:
 * node scripts/test-auth-node.js
 * 
 * Or with specific test:
 * node scripts/test-auth-node.js --test=middleware
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  test: (msg) => console.log(`${colors.cyan}🧪${colors.reset} ${msg}`),
  group: (msg) => console.log(`\n${colors.bright}${msg}${colors.reset}`),
  result: (test, passed) => console.log(`  ${passed ? '✅' : '❌'} ${test}`)
};

class AuthenticationTester {
  constructor() {
    this.results = {};
    this.projectRoot = path.resolve(__dirname, '..');
  }

  // Test 1: File Structure Validation
  testFileStructure() {
    log.group('📁 Testing File Structure');
    
    const requiredFiles = [
      'utils/cookieHelper.js',
      'context/ToastContext.jsx',
      'components/ui/Toast.jsx',
      'components/ui/ConfirmDialog.jsx',
      'components/DeploymentMonitor.jsx',
      'utils/authTestHelper.js',
      'app/api/health/route.js',
      'docs/AUTHENTICATION_IMPROVEMENTS.md'
    ];

    const results = requiredFiles.map(file => {
      const filePath = path.join(this.projectRoot, file);
      const exists = fs.existsSync(filePath);
      log.result(file, exists);
      return { file, exists };
    });

    const allExist = results.every(r => r.exists);
    log.result('All required files exist', allExist);
    
    return allExist;
  }

  // Test 2: Code Quality Validation
  testCodeQuality() {
    log.group('🔍 Testing Code Quality');
    
    const tests = [];

    // Test cookie helper exports
    try {
      const cookieHelperPath = path.join(this.projectRoot, 'utils/cookieHelper.js');
      const cookieHelperContent = fs.readFileSync(cookieHelperPath, 'utf8');
      
      const requiredExports = [
        'clearAuthCookies',
        'setAuthCookies', 
        'getAuthCookies',
        'isAuthenticated',
        'getToken',
        'getUser'
      ];

      const hasAllExports = requiredExports.every(exportName => 
        cookieHelperContent.includes(`export const ${exportName}`) ||
        cookieHelperContent.includes(`${exportName}:`)
      );

      tests.push({ name: 'Cookie helper has all required exports', passed: hasAllExports });
    } catch (error) {
      tests.push({ name: 'Cookie helper validation', passed: false, error: error.message });
    }

    // Test middleware improvements
    try {
      const middlewarePath = path.join(this.projectRoot, 'middleware.js');
      const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
      
      const hasErrorHandling = middlewareContent.includes('try {') && middlewareContent.includes('catch');
      const hasLogging = middlewareContent.includes('logMiddlewareAction');
      const hasFlexibleRedirect = middlewareContent.includes('allowedPagesForCompletedUsers');

      tests.push({ name: 'Middleware has error handling', passed: hasErrorHandling });
      tests.push({ name: 'Middleware has logging', passed: hasLogging });
      tests.push({ name: 'Middleware has flexible redirect logic', passed: hasFlexibleRedirect });
    } catch (error) {
      tests.push({ name: 'Middleware validation', passed: false, error: error.message });
    }

    // Test UserProvider improvements
    try {
      const userProviderPath = path.join(this.projectRoot, 'context/UserProvider.jsx');
      const userProviderContent = fs.readFileSync(userProviderPath, 'utf8');
      
      const hasToastImport = userProviderContent.includes('useToast');
      const hasCookieHelperImport = userProviderContent.includes('cookieHelper');
      const hasLogoutLoading = userProviderContent.includes('logoutLoading');

      tests.push({ name: 'UserProvider uses toast notifications', passed: hasToastImport });
      tests.push({ name: 'UserProvider uses cookie helper', passed: hasCookieHelperImport });
      tests.push({ name: 'UserProvider has logout loading state', passed: hasLogoutLoading });
    } catch (error) {
      tests.push({ name: 'UserProvider validation', passed: false, error: error.message });
    }

    // Display results
    tests.forEach(test => {
      log.result(test.name, test.passed);
      if (!test.passed && test.error) {
        console.log(`    Error: ${test.error}`);
      }
    });

    const allPassed = tests.every(test => test.passed);
    log.result('All code quality tests passed', allPassed);
    
    return allPassed;
  }

  // Test 3: API Endpoint Validation
  async testAPIEndpoints() {
    log.group('🌐 Testing API Endpoints');
    
    try {
      // Test health endpoint file
      const healthPath = path.join(this.projectRoot, 'app/api/health/route.js');
      const healthContent = fs.readFileSync(healthPath, 'utf8');
      
      const hasGETHandler = healthContent.includes('export async function GET');
      const hasHEADHandler = healthContent.includes('export async function HEAD');
      const hasErrorHandling = healthContent.includes('try {') && healthContent.includes('catch');

      log.result('Health API has GET handler', hasGETHandler);
      log.result('Health API has HEAD handler', hasHEADHandler);
      log.result('Health API has error handling', hasErrorHandling);

      return hasGETHandler && hasHEADHandler && hasErrorHandling;
    } catch (error) {
      log.error(`API endpoint validation failed: ${error.message}`);
      return false;
    }
  }

  // Test 4: Component Integration
  testComponentIntegration() {
    log.group('🔧 Testing Component Integration');
    
    const tests = [];

    // Test AppShell provider order
    try {
      const appShellPath = path.join(this.projectRoot, 'components/AppShell.js');
      const appShellContent = fs.readFileSync(appShellPath, 'utf8');
      
      const hasToastProvider = appShellContent.includes('ToastProvider');
      const hasUserProvider = appShellContent.includes('UserProvider');
      const hasDeploymentMonitor = appShellContent.includes('DeploymentMonitor');

      // Check provider order (ToastProvider should come before UserProvider)
      const toastIndex = appShellContent.indexOf('<ToastProvider>');
      const userIndex = appShellContent.indexOf('<UserProvider>');
      const correctOrder = toastIndex < userIndex && toastIndex !== -1 && userIndex !== -1;

      tests.push({ name: 'AppShell includes ToastProvider', passed: hasToastProvider });
      tests.push({ name: 'AppShell includes UserProvider', passed: hasUserProvider });
      tests.push({ name: 'AppShell includes DeploymentMonitor', passed: hasDeploymentMonitor });
      tests.push({ name: 'Provider order is correct', passed: correctOrder });
    } catch (error) {
      tests.push({ name: 'AppShell validation', passed: false, error: error.message });
    }

    // Test Header component improvements
    try {
      const headerPath = path.join(this.projectRoot, 'components/Header.jsx');
      const headerContent = fs.readFileSync(headerPath, 'utf8');
      
      const hasConfirmDialog = headerContent.includes('ConfirmDialog');
      const hasToastUsage = headerContent.includes('useToast');
      const hasLogoutLoading = headerContent.includes('logoutLoading');

      tests.push({ name: 'Header has confirmation dialog', passed: hasConfirmDialog });
      tests.push({ name: 'Header uses toast notifications', passed: hasToastUsage });
      tests.push({ name: 'Header handles logout loading', passed: hasLogoutLoading });
    } catch (error) {
      tests.push({ name: 'Header validation', passed: false, error: error.message });
    }

    // Display results
    tests.forEach(test => {
      log.result(test.name, test.passed);
      if (!test.passed && test.error) {
        console.log(`    Error: ${test.error}`);
      }
    });

    const allPassed = tests.every(test => test.passed);
    log.result('All component integration tests passed', allPassed);
    
    return allPassed;
  }

  // Test 5: Documentation Validation
  testDocumentation() {
    log.group('📚 Testing Documentation');
    
    try {
      const docPath = path.join(this.projectRoot, 'docs/AUTHENTICATION_IMPROVEMENTS.md');
      const docContent = fs.readFileSync(docPath, 'utf8');
      
      const requiredSections = [
        '# Authentication Flow Improvements',
        '## 🚨 Các Vấn Đề Đã Được Giải Quyết',
        '## 🛠️ Các File Đã Được Cải Thiện',
        '## 🎯 Các Tính Năng Mới',
        '## 🧪 Testing & Validation'
      ];

      const results = requiredSections.map(section => {
        const exists = docContent.includes(section);
        log.result(`Documentation has section: ${section}`, exists);
        return exists;
      });

      const allSectionsExist = results.every(exists => exists);
      log.result('All documentation sections exist', allSectionsExist);
      
      return allSectionsExist;
    } catch (error) {
      log.error(`Documentation validation failed: ${error.message}`);
      return false;
    }
  }

  // Run all tests
  async runAllTests() {
    log.info('Starting Node.js Authentication Tests...\n');
    
    const testResults = {
      fileStructure: this.testFileStructure(),
      codeQuality: this.testCodeQuality(),
      apiEndpoints: await this.testAPIEndpoints(),
      componentIntegration: this.testComponentIntegration(),
      documentation: this.testDocumentation()
    };

    // Summary
    log.group('📊 Test Results Summary');
    Object.entries(testResults).forEach(([test, passed]) => {
      log.result(test, passed);
    });

    const allPassed = Object.values(testResults).every(result => result);
    
    if (allPassed) {
      log.success('\n🎉 ALL TESTS PASSED! Authentication improvements are ready for production.');
    } else {
      log.error('\n💥 SOME TESTS FAILED! Please check the issues above.');
    }

    return testResults;
  }

  // Run specific test
  async runSpecificTest(testName) {
    log.info(`Running specific test: ${testName}\n`);
    
    switch (testName) {
      case 'files':
      case 'structure':
        return this.testFileStructure();
      case 'quality':
      case 'code':
        return this.testCodeQuality();
      case 'api':
      case 'endpoints':
        return await this.testAPIEndpoints();
      case 'components':
      case 'integration':
        return this.testComponentIntegration();
      case 'docs':
      case 'documentation':
        return this.testDocumentation();
      default:
        log.error(`Unknown test: ${testName}`);
        log.info('Available tests: files, quality, api, components, docs');
        return false;
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const testArg = args.find(arg => arg.startsWith('--test='));
  const specificTest = testArg ? testArg.split('=')[1] : null;

  const tester = new AuthenticationTester();
  
  if (specificTest) {
    await tester.runSpecificTest(specificTest);
  } else {
    await tester.runAllTests();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    log.error(`Test execution failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = AuthenticationTester;
