#!/bin/bash

# HTTP-based Authentication Testing Script
# Test API endpoints and authentication flow via HTTP requests
# 
# Usage:
# chmod +x scripts/test-auth-http.sh
# ./scripts/test-auth-http.sh
# 
# Or with specific base URL:
# ./scripts/test-auth-http.sh http://localhost:3000

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Base URL (default to localhost:3000)
BASE_URL=${1:-"http://localhost:3000"}

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

log_success() {
    echo -e "${GREEN}✅${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}❌${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

log_group() {
    echo -e "\n${BLUE}$1${NC}"
    echo "=================================="
}

# Test 1: Health API Endpoint
test_health_api() {
    log_group "🏥 Testing Health API"
    
    # Test GET request
    log_info "Testing GET /api/health..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$BASE_URL/api/health")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Health API GET request successful (200)"
        
        # Check if response contains expected fields
        if echo "$body" | grep -q '"status"' && echo "$body" | grep -q '"timestamp"'; then
            log_success "Health API response contains required fields"
        else
            log_error "Health API response missing required fields"
        fi
    else
        log_error "Health API GET request failed (HTTP $http_code)"
    fi
    
    # Test HEAD request
    log_info "Testing HEAD /api/health..."
    head_response=$(curl -s -I "$BASE_URL/api/health" | head -n 1)
    if echo "$head_response" | grep -q "200"; then
        log_success "Health API HEAD request successful"
    else
        log_error "Health API HEAD request failed"
    fi
}

# Test 2: Authentication API Endpoints
test_auth_endpoints() {
    log_group "🔐 Testing Authentication Endpoints"
    
    # Test login endpoint (should return error without credentials)
    log_info "Testing POST /api/auth/login (without credentials)..."
    login_response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{}' \
        "$BASE_URL/api/auth/login")
    
    login_http_code=$(echo $login_response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    
    if [ "$login_http_code" -eq 400 ] || [ "$login_http_code" -eq 401 ] || [ "$login_http_code" -eq 500 ]; then
        log_success "Login endpoint properly rejects empty credentials"
    else
        log_error "Login endpoint should reject empty credentials (got HTTP $login_http_code)"
    fi
    
    # Test logout endpoint
    log_info "Testing POST /api/auth/logout..."
    logout_response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        "$BASE_URL/api/auth/logout")
    
    logout_http_code=$(echo $logout_response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    
    if [ "$logout_http_code" -eq 200 ] || [ "$logout_http_code" -eq 401 ]; then
        log_success "Logout endpoint responds appropriately"
    else
        log_error "Logout endpoint unexpected response (HTTP $logout_http_code)"
    fi
}

# Test 3: Static Assets and Pages
test_static_assets() {
    log_group "📄 Testing Static Assets and Pages"
    
    # Test main pages accessibility
    pages=("/" "/dang-nhap" "/dang-ky")
    
    for page in "${pages[@]}"; do
        log_info "Testing page: $page"
        page_response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$BASE_URL$page")
        page_http_code=$(echo $page_response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        
        if [ "$page_http_code" -eq 200 ]; then
            log_success "Page $page is accessible"
        else
            log_error "Page $page is not accessible (HTTP $page_http_code)"
        fi
    done
}

# Test 4: Middleware Behavior
test_middleware_behavior() {
    log_group "🛡️ Testing Middleware Behavior"
    
    # Test protected routes (should redirect to login)
    protected_routes=("/quan-ly" "/tai-khoan" "/thong-bao")
    
    for route in "${protected_routes[@]}"; do
        log_info "Testing protected route: $route"
        
        # Use -L to follow redirects and check final status
        redirect_response=$(curl -s -w "HTTPSTATUS:%{http_code}" -L "$BASE_URL$route")
        redirect_http_code=$(echo $redirect_response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        
        # Check if we get redirected (302/301) or reach login page (200)
        if [ "$redirect_http_code" -eq 200 ] || [ "$redirect_http_code" -eq 302 ] || [ "$redirect_http_code" -eq 301 ]; then
            log_success "Protected route $route handles authentication properly"
        else
            log_error "Protected route $route unexpected behavior (HTTP $redirect_http_code)"
        fi
    done
}

# Test 5: CORS and Security Headers
test_security_headers() {
    log_group "🔒 Testing Security Headers"
    
    # Test CORS headers
    log_info "Testing CORS headers..."
    cors_response=$(curl -s -I -H "Origin: http://localhost:3000" "$BASE_URL/api/health")
    
    if echo "$cors_response" | grep -qi "access-control"; then
        log_success "CORS headers are present"
    else
        log_warning "CORS headers not found (may be intentional)"
    fi
    
    # Test security headers
    log_info "Testing security headers..."
    security_response=$(curl -s -I "$BASE_URL/")
    
    security_headers=("X-Frame-Options" "X-Content-Type-Options" "Referrer-Policy")
    for header in "${security_headers[@]}"; do
        if echo "$security_response" | grep -qi "$header"; then
            log_success "Security header $header is present"
        else
            log_warning "Security header $header not found"
        fi
    done
}

# Test 6: Performance and Response Times
test_performance() {
    log_group "⚡ Testing Performance"
    
    # Test response times for key endpoints
    endpoints=("/" "/api/health" "/dang-nhap")
    
    for endpoint in "${endpoints[@]}"; do
        log_info "Testing response time for: $endpoint"
        
        response_time=$(curl -s -w "%{time_total}" -o /dev/null "$BASE_URL$endpoint")
        
        # Convert to milliseconds for easier reading
        response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d. -f1)
        
        if [ "$response_time_ms" -lt 2000 ]; then
            log_success "Response time for $endpoint: ${response_time_ms}ms (Good)"
        elif [ "$response_time_ms" -lt 5000 ]; then
            log_warning "Response time for $endpoint: ${response_time_ms}ms (Acceptable)"
        else
            log_error "Response time for $endpoint: ${response_time_ms}ms (Slow)"
        fi
    done
}

# Main execution
main() {
    echo -e "${BLUE}🧪 HTTP Authentication Testing${NC}"
    echo "Testing against: $BASE_URL"
    echo "=================================="
    
    # Check if server is running
    log_info "Checking if server is running..."
    if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null; then
        log_success "Server is running at $BASE_URL"
    else
        log_error "Server is not running at $BASE_URL"
        echo "Please start the development server with: npm run dev"
        exit 1
    fi
    
    # Run all tests
    test_health_api
    test_auth_endpoints
    test_static_assets
    test_middleware_behavior
    test_security_headers
    test_performance
    
    # Summary
    echo -e "\n${BLUE}📊 Test Results Summary${NC}"
    echo "=================================="
    echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "\n${GREEN}🎉 ALL HTTP TESTS PASSED!${NC}"
        exit 0
    else
        echo -e "\n${RED}💥 SOME HTTP TESTS FAILED!${NC}"
        exit 1
    fi
}

# Check dependencies
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        log_warning "bc is not installed, performance tests will be skipped"
    fi
}

# Run the tests
check_dependencies
main
