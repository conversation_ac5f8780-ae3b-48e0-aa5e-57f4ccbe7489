/**
 * Authentication Test Helper
 * Utility functions để test authentication flow sau khi cải thiện
 */

import { clearAuthCookies, setAuthCookies, getAuthCookies, isAuthenticated, debugAuthCookies } from './cookieHelper';

// Test scenarios
export const TEST_SCENARIOS = {
  GUEST_USER: 'guest_user',
  AUTHENTICATED_USER_NO_ORDER: 'authenticated_user_no_order', 
  AUTHENTICATED_USER_WITH_ORDER: 'authenticated_user_with_order',
  EXPIRED_TOKEN: 'expired_token',
  INVALID_COOKIES: 'invalid_cookies'
};

// Mock user data for testing
export const MOCK_USERS = {
  [TEST_SCENARIOS.AUTHENTICATED_USER_NO_ORDER]: {
    token: 'mock_token_no_order_123',
    user: {
      id: 1,
      email: '<EMAIL>',
      fullname: 'Test User',
      phone: '**********',
      verified_otp: true,
      provider: 'local'
    },
    hasCompletedOrder: false,
    completedOrderInfo: null
  },
  
  [TEST_SCENARIOS.AUTHENTICATED_USER_WITH_ORDER]: {
    token: 'mock_token_with_order_456',
    user: {
      id: 2,
      email: '<EMAIL>',
      fullname: 'Premium User',
      phone: '**********',
      verified_otp: true,
      provider: 'local'
    },
    hasCompletedOrder: true,
    completedOrderInfo: {
      id: 1,
      course_name: 'Khóa học Hóa học cơ bản',
      amount: 500000,
      status: 'completed'
    }
  }
};

/**
 * Setup test scenario
 * @param {string} scenario - Test scenario from TEST_SCENARIOS
 */
export const setupTestScenario = (scenario) => {
  console.group(`🧪 Setting up test scenario: ${scenario}`);
  
  // Clear existing auth data
  clearAuthCookies();
  
  switch (scenario) {
    case TEST_SCENARIOS.GUEST_USER:
      console.log('✅ Guest user setup complete (no auth data)');
      break;
      
    case TEST_SCENARIOS.AUTHENTICATED_USER_NO_ORDER:
      setAuthCookies(MOCK_USERS[scenario]);
      console.log('✅ Authenticated user (no order) setup complete');
      break;
      
    case TEST_SCENARIOS.AUTHENTICATED_USER_WITH_ORDER:
      setAuthCookies(MOCK_USERS[scenario]);
      console.log('✅ Authenticated user (with order) setup complete');
      break;
      
    case TEST_SCENARIOS.EXPIRED_TOKEN:
      // Set expired cookies
      document.cookie = 'access_token=expired_token; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      console.log('✅ Expired token setup complete');
      break;
      
    case TEST_SCENARIOS.INVALID_COOKIES:
      // Set malformed cookies
      document.cookie = 'user_data=invalid_json; path=/;';
      document.cookie = 'access_token=invalid_token; path=/;';
      console.log('✅ Invalid cookies setup complete');
      break;
      
    default:
      console.error('❌ Unknown test scenario:', scenario);
  }
  
  debugAuthCookies();
  console.groupEnd();
};

/**
 * Test authentication state
 */
export const testAuthenticationState = () => {
  console.group('🔍 Testing Authentication State');
  
  const authData = getAuthCookies();
  const authenticated = isAuthenticated();
  
  console.log('Auth Data:', authData);
  console.log('Is Authenticated:', authenticated);
  console.log('Has Completed Order:', authData.hasCompletedOrder);
  
  // Validate consistency
  const isConsistent = authenticated === !!(authData.token && authData.user);
  console.log('State Consistency:', isConsistent ? '✅ Consistent' : '❌ Inconsistent');
  
  console.groupEnd();
  return { authData, authenticated, isConsistent };
};

/**
 * Test expected redirects for different scenarios
 * @param {string} currentPath - Current page path
 * @param {string} scenario - Test scenario
 */
export const testExpectedRedirects = (currentPath, scenario) => {
  console.group(`🔄 Testing Expected Redirects for ${scenario} on ${currentPath}`);
  
  const authData = getAuthCookies();
  const authenticated = isAuthenticated();
  
  let expectedRedirect = null;
  let shouldAllowAccess = true;
  
  // Define expected behavior based on scenario and path
  switch (scenario) {
    case TEST_SCENARIOS.GUEST_USER:
      if (['/quan-ly', '/tai-khoan', '/thong-bao'].some(path => currentPath.startsWith(path))) {
        expectedRedirect = '/dang-nhap';
        shouldAllowAccess = false;
      }
      break;
      
    case TEST_SCENARIOS.AUTHENTICATED_USER_NO_ORDER:
      if (currentPath === '/quan-ly') {
        expectedRedirect = '/';
        shouldAllowAccess = false;
      } else if (['/dang-nhap', '/dang-ky'].includes(currentPath)) {
        expectedRedirect = '/';
        shouldAllowAccess = false;
      }
      break;
      
    case TEST_SCENARIOS.AUTHENTICATED_USER_WITH_ORDER:
      if (['/dang-nhap', '/dang-ky'].includes(currentPath)) {
        expectedRedirect = '/quan-ly';
        shouldAllowAccess = false;
      } else if (!['/quan-ly', '/tai-khoan', '/thong-bao', '/', '/khoa-hoc', '/bai-viet', '/hoc-tai-trung-tam'].some(path => 
        currentPath === path || currentPath.startsWith(path + '/'))) {
        expectedRedirect = '/quan-ly';
        shouldAllowAccess = false;
      }
      break;
  }
  
  console.log('Current Path:', currentPath);
  console.log('Expected Redirect:', expectedRedirect || 'None');
  console.log('Should Allow Access:', shouldAllowAccess);
  
  console.groupEnd();
  return { expectedRedirect, shouldAllowAccess };
};

/**
 * Test logout functionality
 */
export const testLogoutFunctionality = async () => {
  console.group('🚪 Testing Logout Functionality');
  
  // Setup authenticated user
  setupTestScenario(TEST_SCENARIOS.AUTHENTICATED_USER_WITH_ORDER);
  
  console.log('Before logout:');
  testAuthenticationState();
  
  // Simulate logout
  clearAuthCookies();
  
  console.log('After logout:');
  const result = testAuthenticationState();
  
  const logoutSuccess = !result.authenticated && !result.authData.token && !result.authData.user;
  console.log('Logout Success:', logoutSuccess ? '✅ Success' : '❌ Failed');
  
  console.groupEnd();
  return logoutSuccess;
};

/**
 * Run comprehensive authentication tests
 */
export const runAuthenticationTests = () => {
  console.group('🧪 Running Comprehensive Authentication Tests');
  
  const results = {
    scenarios: {},
    logout: false,
    overall: true
  };
  
  // Test each scenario
  Object.values(TEST_SCENARIOS).forEach(scenario => {
    console.log(`\n--- Testing ${scenario} ---`);
    setupTestScenario(scenario);
    const authState = testAuthenticationState();
    
    // Test common paths
    const testPaths = ['/', '/quan-ly', '/dang-nhap', '/tai-khoan'];
    const redirectTests = testPaths.map(path => testExpectedRedirects(path, scenario));
    
    results.scenarios[scenario] = {
      authState,
      redirectTests,
      success: authState.isConsistent
    };
    
    if (!authState.isConsistent) {
      results.overall = false;
    }
  });
  
  // Test logout
  results.logout = testLogoutFunctionality();
  if (!results.logout) {
    results.overall = false;
  }
  
  console.log('\n🎯 Test Results Summary:');
  console.log('Overall Success:', results.overall ? '✅ PASS' : '❌ FAIL');
  console.log('Logout Test:', results.logout ? '✅ PASS' : '❌ FAIL');
  
  Object.entries(results.scenarios).forEach(([scenario, result]) => {
    console.log(`${scenario}:`, result.success ? '✅ PASS' : '❌ FAIL');
  });
  
  console.groupEnd();
  return results;
};

/**
 * Quick validation function for production use
 */
export const validateAuthenticationSetup = () => {
  try {
    const authData = getAuthCookies();
    const authenticated = isAuthenticated();
    
    // Basic validation
    if (authenticated && (!authData.token || !authData.user)) {
      console.warn('⚠️ Authentication state inconsistency detected');
      return false;
    }
    
    if (!authenticated && (authData.token || authData.user)) {
      console.warn('⚠️ Stale authentication data detected');
      clearAuthCookies(); // Auto-cleanup
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Authentication validation error:', error);
    return false;
  }
};

export default {
  setupTestScenario,
  testAuthenticationState,
  testExpectedRedirects,
  testLogoutFunctionality,
  runAuthenticationTests,
  validateAuthenticationSetup,
  TEST_SCENARIOS,
  MOCK_USERS
};
