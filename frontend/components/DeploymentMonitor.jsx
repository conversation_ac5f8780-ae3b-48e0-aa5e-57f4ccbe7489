"use client";

import React, { useState, useEffect } from 'react';
import { useToast } from '../context/ToastContext';

/**
 * DeploymentMonitor Component
 * Monitors deployment status and shows notifications
 * Only active in production environment
 */
const DeploymentMonitor = () => {
  const [deploymentStatus, setDeploymentStatus] = useState('idle');
  const [lastChecked, setLastChecked] = useState(null);
  const toast = useToast();

  // Only run in production
  const isProduction = process.env.NODE_ENV === 'production';
  
  useEffect(() => {
    if (!isProduction) return;

    // Check deployment status periodically
    const checkDeploymentStatus = async () => {
      try {
        // Simple health check to detect new deployments
        const response = await fetch('/api/health', {
          method: 'GET',
          cache: 'no-cache'
        });
        
        if (response.ok) {
          const data = await response.json();
          const currentTimestamp = data.timestamp || Date.now();
          
          // If this is not the first check and timestamp changed significantly
          if (lastChecked && Math.abs(currentTimestamp - lastChecked) > 60000) {
            setDeploymentStatus('updated');
            toast.info('Ứng dụng đã được cập nhật', 'Làm mới trang để có trải nghiệm tốt nhất');
          }
          
          setLastChecked(currentTimestamp);
          setDeploymentStatus('healthy');
        } else {
          setDeploymentStatus('error');
        }
      } catch (error) {
        console.warn('Deployment monitor check failed:', error);
        setDeploymentStatus('error');
      }
    };

    // Initial check after 5 seconds
    const initialTimeout = setTimeout(checkDeploymentStatus, 5000);
    
    // Then check every 5 minutes
    const interval = setInterval(checkDeploymentStatus, 5 * 60 * 1000);

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, [isProduction, lastChecked, toast]);

  // Handle page visibility change to check for updates when user returns
  useEffect(() => {
    if (!isProduction) return;

    const handleVisibilityChange = () => {
      if (!document.hidden && deploymentStatus === 'updated') {
        // Show refresh suggestion when user returns to tab
        toast.warning('Có bản cập nhật mới', 'Nhấn F5 để tải lại trang');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isProduction, deploymentStatus, toast]);

  // Don't render anything in development or if no status
  if (!isProduction || deploymentStatus === 'idle') {
    return null;
  }

  // Optional: Render a small status indicator (hidden by default)
  return (
    <div className="hidden">
      {/* This component primarily works through side effects (toast notifications) */}
      {/* Status: {deploymentStatus} */}
    </div>
  );
};

export default DeploymentMonitor;
